import React, { useState, useMemo, useRef } from 'react';
import { Plus, Minus, ShoppingCart, Users, Star, Utensils, Search, X, ChevronLeft, ChevronRight, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface MenuItem {
  id: string;
  name: string;
  price: number;
  description?: string;
  category: string;
  isVegetarian?: boolean;
  isSpicy?: boolean;
  isSignature?: boolean;
}

interface OrderItem extends MenuItem {
  quantity: number;
}

const menuData: MenuItem[] = [
  // Appetizers
  { id: 'app1', name: 'Butter Fried Onion Rings', price: 730, category: 'Appetizers' },
  { id: 'app2', name: 'Shredded Potato', price: 730, category: 'Appetizers' },

  // Soup
  { id: 'soup1', name: 'Chicken Soup', price: 850, category: 'Soup' },
  { id: 'soup2', name: 'Chicken with Sweet Corn Soup', price: 950, category: 'Soup' },
  { id: 'soup3', name: 'Vegetable Soup', price: 650, category: 'Soup', isVegetarian: true },
  { id: 'soup4', name: 'Tomato and Egg Soup', price: 790, category: 'Soup' },
  { id: 'soup5', name: 'Tom Yum (Sea Food Soup)', price: 990, category: 'Soup', isSpicy: true, description: 'Traditional hot and sour Thai soup with seafood' },

  // Salad
  { id: 'salad1', name: 'Coleslaw Salad', price: 930, category: 'Salad', isVegetarian: true },
  { id: 'salad2', name: 'Mix Vegetable Salad', price: 930, category: 'Salad', isVegetarian: true },
  { id: 'salad3', name: 'Tomato Onion Salad', price: 930, category: 'Salad', isVegetarian: true },
  { id: 'salad4', name: 'Chefs Special Salad', price: 1690, category: 'Salad', description: 'A refreshing blend of seasonal ingredients with our signature dressing' },

  // Rice Dishes
  { id: 'rice1', name: 'Vegetable Fried Rice', price: 790, category: 'Rice Dishes', isVegetarian: true },
  { id: 'rice2', name: 'Garlic Rice', price: 890, category: 'Rice Dishes', isVegetarian: true },
  { id: 'rice3', name: 'Egg Fried Rice', price: 890, category: 'Rice Dishes' },
  { id: 'rice4', name: 'Chicken Fried Rice', price: 1250, category: 'Rice Dishes' },
  { id: 'rice5', name: 'Sea Food Fried Rice', price: 1390, category: 'Rice Dishes' },
  { id: 'rice6', name: 'Steamed Rice', price: 590, category: 'Rice Dishes', isVegetarian: true },
  { id: 'rice7', name: 'Mixed Fried Rice', price: 1390, category: 'Rice Dishes' },
  { id: 'rice8', name: 'Chicken Mix Fried Rice', price: 1250, category: 'Rice Dishes' },
  { id: 'rice9', name: 'Fish Fried Rice', price: 1290, category: 'Rice Dishes' },
  { id: 'rice10', name: 'Beef Fried Rice', price: 1390, category: 'Rice Dishes' },

  // Noodles
  { id: 'noodle1', name: 'Vegetable Fried Noodles', price: 850, category: 'Noodles', isVegetarian: true },
  { id: 'noodle2', name: 'Garlic Noodles', price: 950, category: 'Noodles', isVegetarian: true },
  { id: 'noodle3', name: 'Egg Noodles', price: 950, category: 'Noodles' },
  { id: 'noodle4', name: 'Chicken Fried Noodles', price: 1300, category: 'Noodles' },
  { id: 'noodle5', name: 'Sea Food Fried Noodles', price: 1430, category: 'Noodles' },
  { id: 'noodle6', name: 'Steamed Noodles', price: 690, category: 'Noodles', isVegetarian: true },
  { id: 'noodle7', name: 'Mixed Fried Noodles', price: 1430, category: 'Noodles' },
  { id: 'noodle8', name: 'Fish Fried Noodles', price: 1400, category: 'Noodles' },
  { id: 'noodle9', name: 'Beef Noodles', price: 1430, category: 'Noodles' },

  // Oriental Dishes
  { id: 'oriental1', name: 'Nasigoreng Fried Rice', price: 1430, category: 'Oriental Dishes', isSignature: true, description: 'Indonesian-inspired fried rice with chicken, shrimp, vegetables, and a fried egg' },
  { id: 'oriental2', name: 'Chicken Biriyani', price: 1430, category: 'Oriental Dishes' },
  { id: 'oriental3', name: 'Mongolian Rice', price: 1430, category: 'Oriental Dishes' },
  { id: 'oriental4', name: 'Spice & Herb Special Rice or Noodles', price: 2350, category: 'Oriental Dishes', isSignature: true, description: 'Our signature dish featuring a premium mix of chicken, seafood, and vegetables' },

  // Chicken Dishes
  { id: 'chicken1', name: 'Devilled Chicken', price: 1430, category: 'Chicken Dishes', isSpicy: true },
  { id: 'chicken2', name: 'Chilli Chicken with Cashewnuts', price: 1530, category: 'Chicken Dishes', isSpicy: true },
  { id: 'chicken3', name: 'Sweet and Sour Chicken', price: 1530, category: 'Chicken Dishes' },
  { id: 'chicken4', name: 'Kankun with Garlic Chicken', price: 1530, category: 'Chicken Dishes' },
  { id: 'chicken5', name: 'Fried Chicken', price: 1530, category: 'Chicken Dishes', description: 'Our famous Kandy-style fried chicken' },
  { id: 'chicken6', name: 'KFC (Kandy Fried Chicken)', price: 1530, category: 'Chicken Dishes' },

  // Koththu (popular items)
  { id: 'kottu1', name: 'Vegetable Koththu', price: 990, category: 'Koththu', isVegetarian: true },
  { id: 'kottu2', name: 'Egg Koththu', price: 1100, category: 'Koththu' },
  { id: 'kottu3', name: 'Fish Koththu', price: 1250, category: 'Koththu' },
  { id: 'kottu4', name: 'Chicken Koththu', price: 1390, category: 'Koththu' },
  { id: 'kottu5', name: 'Cheese & Chicken Koththu', price: 1590, category: 'Koththu', isSignature: true, description: 'Our popular fusion dish combining traditional koththu with cheese' },
  { id: 'kottu6', name: 'Beef Koththu', price: 1490, category: 'Koththu' },

  // Beverages
  { id: 'bev1', name: 'Cup of Milk Tea or Coffee', price: 350, category: 'Beverages' },
  { id: 'bev2', name: 'Lime Juice', price: 390, category: 'Beverages', isVegetarian: true },
  { id: 'bev3', name: 'Pineapple/Papaya/Mango', price: 390, category: 'Beverages', isVegetarian: true },
  { id: 'bev4', name: 'Coke/Sprite/Fanta (400ml)', price: 350, category: 'Beverages' },
  { id: 'bev5', name: 'Mineral Water (500ml)', price: 180, category: 'Beverages' },

  // Desserts
  { id: 'dessert1', name: 'Watalappan', price: 450, category: 'Desserts', description: 'Traditional Sri Lankan coconut custard dessert with jaggery' },
  { id: 'dessert2', name: 'Cream Caramel', price: 450, category: 'Desserts' },
  { id: 'dessert3', name: 'Fresh Fruit Platter', price: 630, category: 'Desserts', isVegetarian: true },
  { id: 'dessert4', name: 'Ice Cream', price: 390, category: 'Desserts' }
];

const categories = ['All', 'Appetizers', 'Soup', 'Salad', 'Rice Dishes', 'Noodles', 'Oriental Dishes', 'Chicken Dishes', 'Koththu', 'Beverages', 'Desserts'];

const Index = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [cart, setCart] = useState<OrderItem[]>([]);
  const [showBillSplit, setShowBillSplit] = useState(false);
  const [numberOfPeople, setNumberOfPeople] = useState(2);
  const [searchTerm, setSearchTerm] = useState('');
  const categoryScrollRef = useRef<HTMLDivElement>(null);

  const filteredMenu = useMemo(() => {
    let filtered = selectedCategory === 'All'
      ? menuData
      : menuData.filter(item => item.category === selectedCategory);

    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  }, [selectedCategory, searchTerm]);

  const addToCart = (item: MenuItem) => {
    setCart(prev => {
      const existingItem = prev.find(cartItem => cartItem.id === item.id);
      if (existingItem) {
        return prev.map(cartItem =>
          cartItem.id === item.id
            ? { ...cartItem, quantity: cartItem.quantity + 1 }
            : cartItem
        );
      }
      return [...prev, { ...item, quantity: 1 }];
    });
  };

  const removeFromCart = (itemId: string) => {
    setCart(prev => {
      const existingItem = prev.find(cartItem => cartItem.id === itemId);
      if (existingItem && existingItem.quantity > 1) {
        return prev.map(cartItem =>
          cartItem.id === itemId
            ? { ...cartItem, quantity: cartItem.quantity - 1 }
            : cartItem
        );
      }
      return prev.filter(cartItem => cartItem.id !== itemId);
    });
  };

  const clearSearch = () => {
    setSearchTerm('');
  };

  const scrollCategories = (direction: 'left' | 'right') => {
    if (categoryScrollRef.current) {
      const scrollAmount = 200;
      const currentScroll = categoryScrollRef.current.scrollLeft;
      const targetScroll = direction === 'left'
        ? currentScroll - scrollAmount
        : currentScroll + scrollAmount;

      categoryScrollRef.current.scrollTo({
        left: targetScroll,
        behavior: 'smooth'
      });
    }
  };

  const subtotalAmount = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const serviceFee = subtotalAmount * 0.1; // 10% service fee
  const totalAmount = subtotalAmount + serviceFee;
  const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
  const amountPerPerson = totalAmount / numberOfPeople;

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-red-600 to-red-700 text-white shadow-xl sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Utensils className="h-6 w-6 md:h-8 md:w-8" />
              <div>
                <h1 className="text-xl md:text-3xl font-bold">Spice & Herb</h1>
                <p className="text-red-100 text-xs md:text-sm">Premium Dining Experience</p>
              </div>
            </div>

            {/* Home Button */}
            <Button
              onClick={() => window.open('https://spiceandherbrestaurant.com', '_blank')}
              variant="outline"
              size="sm"
              className="border-red-300 text-white hover:bg-red-600/30 hover:border-red-200"
            >
              <Home className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Home</span>
            </Button>
          </div>

          {/* Search Bar */}
          <div className="mt-4">
            <div className="relative max-w-md mx-auto lg:mx-0">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-red-300 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search menu items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-10 bg-red-600/20 border-red-400/30 text-white placeholder:text-red-200 focus:bg-red-600/30 focus:border-red-300"
              />
              {searchTerm && (
                <Button
                  onClick={clearSearch}
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 text-red-300 hover:text-white hover:bg-red-600/30"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row">
        {/* Main Content */}
        <div className="flex-1 lg:mr-80">
          {/* Category Filter with Arrow Navigation */}
          <div className="bg-white shadow-sm border-b sticky top-[100px] md:top-[120px] z-40">
            <div className="container mx-auto px-4 py-3">
              <div className="relative">
                {/* Left Arrow */}
                <Button
                  onClick={() => scrollCategories('left')}
                  variant="ghost"
                  size="sm"
                  className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 hover:bg-white shadow-sm md:hidden"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                {/* Category Buttons */}
                <div
                  ref={categoryScrollRef}
                  className="flex gap-2 overflow-x-auto scrollbar-hide px-8 md:px-0 [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]"
                >
                  {categories.map((category) => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? "default" : "outline"}
                      size="sm"
                      className={`whitespace-nowrap text-xs md:text-sm flex-shrink-0 ${
                        selectedCategory === category
                          ? 'bg-red-600 hover:bg-red-700 text-white'
                          : 'border-red-200 text-red-600 hover:bg-red-50'
                      }`}
                      onClick={() => setSelectedCategory(category)}
                    >
                      {category}
                    </Button>
                  ))}
                </div>

                {/* Right Arrow */}
                <Button
                  onClick={() => scrollCategories('right')}
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 hover:bg-white shadow-sm md:hidden"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Search Results Info */}
          {searchTerm && (
            <div className="container mx-auto px-4 py-3">
              <p className="text-sm text-gray-600">
                Found {filteredMenu.length} items for "{searchTerm}"
                {filteredMenu.length === 0 && (
                  <span className="ml-2 text-red-600">- Try a different search term</span>
                )}
              </p>
            </div>
          )}

          {/* Menu Items */}
          <div className="container mx-auto px-4 py-6 pb-8">
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
              {filteredMenu.map((item) => (
                <Card key={item.id} className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-red-100">
                  <CardContent className="p-4 md:p-6">
                    <div className="flex justify-between items-start mb-3 md:mb-4">
                      <h3 className="font-semibold text-sm md:text-lg text-gray-800 group-hover:text-red-600 transition-colors leading-tight">
                        {item.name}
                      </h3>
                      <div className="flex gap-1 ml-2 flex-shrink-0">
                        {item.isSignature && <Badge className="bg-red-600 text-white text-xs"><Star className="h-2 w-2 md:h-3 md:w-3" /></Badge>}
                        {item.isVegetarian && <Badge variant="outline" className="text-green-600 border-green-200 text-xs">V</Badge>}
                        {item.isSpicy && <Badge variant="outline" className="text-red-600 border-red-200 text-xs">🌶️</Badge>}
                      </div>
                    </div>

                    {item.description && (
                      <p className="text-xs md:text-sm text-gray-600 mb-4 md:mb-5 leading-relaxed">{item.description}</p>
                    )}

                    <div className="flex justify-between items-center">
                      <span className="text-lg md:text-xl font-bold text-red-600">Rs. {item.price.toLocaleString()}/=</span>

                      {cart.find(cartItem => cartItem.id === item.id) ? (
                        <div className="flex items-center space-x-2 md:space-x-3 bg-red-50 rounded-lg p-1">
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-6 w-6 md:h-8 md:w-8 p-0 border-red-200 text-red-600 hover:bg-red-100"
                            onClick={() => removeFromCart(item.id)}
                          >
                            <Minus className="h-3 w-3 md:h-4 md:w-4" />
                          </Button>
                          <span className="font-semibold min-w-[16px] md:min-w-[20px] text-center text-sm md:text-base">
                            {cart.find(cartItem => cartItem.id === item.id)?.quantity}
                          </span>
                          <Button
                            size="sm"
                            className="h-6 w-6 md:h-8 md:w-8 p-0 bg-red-600 hover:bg-red-700"
                            onClick={() => addToCart(item)}
                          >
                            <Plus className="h-3 w-3 md:h-4 md:w-4" />
                          </Button>
                        </div>
                      ) : (
                        <Button
                          size="sm"
                          className="bg-red-600 hover:bg-red-700 text-white text-xs md:text-sm"
                          onClick={() => addToCart(item)}
                        >
                          <Plus className="h-3 w-3 md:h-4 md:w-4 mr-1" />
                          Add
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Always Visible Side Cart */}
        <div className="w-full lg:w-80 lg:fixed lg:right-0 lg:top-[120px] lg:h-[calc(100vh-120px)] bg-white shadow-xl border-l border-red-100 z-30 overflow-hidden order-first lg:order-last sticky top-[180px] md:top-[200px] max-h-[calc(100vh-200px)] md:max-h-[calc(100vh-220px)]">
          {/* Mobile Cart Header */}
          <div className="p-4 md:p-6 border-b bg-red-50 lg:block">
            <div className="flex items-center justify-center lg:justify-start">
              <h3 className="font-bold text-red-600 flex items-center text-lg lg:text-base">
                <ShoppingCart className="h-5 w-5 mr-2" />
                Your Order
              </h3>
            </div>
            {totalItems > 0 && (
              <p className="text-sm text-gray-600 mt-2 text-center lg:text-left">{totalItems} items</p>
            )}
          </div>

          {/* Cart Items */}
          <div className="flex-1 overflow-y-auto p-4 md:p-6 h-[280px] md:h-[320px] lg:h-[calc(100%-280px)]">
            {cart.length === 0 ? (
              <div className="text-center py-8 md:py-12 text-gray-500">
                <ShoppingCart className="h-12 w-12 md:h-16 md:w-16 mx-auto mb-3 md:mb-4 text-gray-300" />
                <p className="text-sm md:text-base">Your cart is empty</p>
                <p className="text-xs md:text-sm text-gray-400 mt-1 md:mt-2">Start adding items to see them here</p>
              </div>
            ) : (
              <div className="space-y-3 md:space-y-4">
                {cart.map((item) => (
                  <div key={item.id} className="flex justify-between items-center p-3 md:p-4 bg-red-50 rounded-lg border border-red-100">
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-sm md:text-base truncate">{item.name}</h4>
                      <p className="text-red-600 text-xs md:text-sm mt-1">Rs. {item.price.toLocaleString()}/= each</p>
                    </div>
                    <div className="flex items-center space-x-2 md:space-x-3 ml-3">
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-6 w-6 md:h-8 md:w-8 p-0 border-red-200 text-red-600 hover:bg-red-100"
                        onClick={() => removeFromCart(item.id)}
                      >
                        <Minus className="h-3 w-3 md:h-4 md:w-4" />
                      </Button>
                      <span className="font-semibold min-w-[16px] md:min-w-[20px] text-center text-sm md:text-base">{item.quantity}</span>
                      <Button
                        size="sm"
                        className="h-6 w-6 md:h-8 md:w-8 p-0 bg-red-600 hover:bg-red-700"
                        onClick={() => addToCart(item)}
                      >
                        <Plus className="h-3 w-3 md:h-4 md:w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Cart Summary */}
          {cart.length > 0 && (
            <div className="border-t p-4 md:p-6 bg-white">
              <div className="space-y-2 md:space-y-3 mb-4 md:mb-6">
                <div className="flex justify-between text-sm md:text-base">
                  <span>Subtotal:</span>
                  <span>Rs. {subtotalAmount.toLocaleString()}/=</span>
                </div>
                <div className="flex justify-between text-sm md:text-base">
                  <span>Service Fee (10%):</span>
                  <span>Rs. {serviceFee.toLocaleString()}/=</span>
                </div>
                <Separator className="my-2 md:my-3" />
                <div className="flex justify-between font-bold text-red-600 text-base md:text-lg">
                  <span>Total:</span>
                  <span>Rs. {totalAmount.toLocaleString()}/=</span>
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  className="flex-1 bg-red-600 hover:bg-red-700 text-sm md:text-base py-2 md:py-3"
                  onClick={() => setShowBillSplit(true)}
                >
                  <Users className="h-4 w-4 md:h-5 md:w-5 mr-2" />
                  Split Bill
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Bill Split Dialog */}
      <Dialog open={showBillSplit} onOpenChange={setShowBillSplit}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-red-600">Split the Bill</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            <div>
              <Label htmlFor="people" className="text-sm font-medium">Number of People</Label>
              <Input
                id="people"
                type="number"
                min="1"
                max="20"
                value={numberOfPeople}
                onChange={(e) => setNumberOfPeople(Math.max(1, parseInt(e.target.value) || 1))}
                className="mt-1"
              />
            </div>

            <div className="bg-red-50 p-4 rounded-lg space-y-2">
              <div className="flex justify-between text-sm">
                <span>Subtotal:</span>
                <span>Rs. {subtotalAmount.toLocaleString()}/=</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Service Fee (10%):</span>
                <span>Rs. {serviceFee.toLocaleString()}/=</span>
              </div>
              <div className="flex justify-between">
                <span>Total Amount:</span>
                <span className="font-bold">Rs. {totalAmount.toLocaleString()}/=</span>
              </div>
              <div className="flex justify-between">
                <span>Number of People:</span>
                <span className="font-bold">{numberOfPeople}</span>
              </div>
              <Separator />
              <div className="flex justify-between text-lg">
                <span className="font-bold">Amount per Person:</span>
                <span className="font-bold text-red-600">Rs. {Math.ceil(amountPerPerson).toLocaleString()}/=</span>
              </div>
            </div>

            <p className="text-sm text-gray-600">
              Each person should pay Rs. {Math.ceil(amountPerPerson).toLocaleString()}/=
              {amountPerPerson !== Math.ceil(amountPerPerson) && ' (rounded up for convenience)'}
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Index;
